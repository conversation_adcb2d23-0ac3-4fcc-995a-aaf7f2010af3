// src/features/cart/model/hooks/useQuantityControl.ts
import { useState, useRef, useEffect, useMemo } from 'react';
import { useDispatch } from 'react-redux';

import { useGetCartItemPriceMutation } from '@/entities/price';
import { useAuthContext } from '@/entities/user/model/AuthContext';

import { useSendCartMutation } from '../../api';
import { addToCart, changeQuantity, removeFromCart } from '../cartSlice';

import type { CartProduct } from '../types';

interface UseQuantityControlProps {
  product: CartProduct;
  enableRemove?: boolean;
}

interface UseQuantityControlReturn {
  quantity: number;
  isLoading: boolean;
  handleIncrease: (e: React.MouseEvent) => Promise<void>;
  handleDecrease: (e: React.MouseEvent) => Promise<void>;
  isMinQuantity: boolean;
  isMaxQuantity: boolean;
  stockLimitMessage: string | null;
}

export const useQuantityControl = ({
  product,
  enableRemove = false,
}: UseQuantityControlProps): UseQuantityControlReturn => {
  // const { isAuthenticated } = useAuthContext();
  const dispatch = useDispatch();
  const [quantity, setQuantity] = useState(product.quantity || 1);
  const previousQuantity = useRef(product.quantity);
  const debounceTimer = useRef<NodeJS.Timeout | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Track locally how many additions are pending to prevent exceeding stock during rapid clicks
  const [pendingAdds, setPendingAdds] = useState(0);

  useEffect(() => {
    console.log('[useQuantityControl] pendingAdds updated:', pendingAdds, 'product.id:', product.id);
  }, [pendingAdds, product.id]);

  const [sendCart, { isLoading: sendCartLoading }] = useSendCartMutation();
  const [getItemPrice, { isLoading: priceLoading }] =
    useGetCartItemPriceMutation();

  useEffect(() => {
    setQuantity(Number(product.quantity));
  }, [product.quantity]);

  const respectStockLimits = useMemo(() => {
    const shouldRespect = product.availability?.preorder === null;
    console.log('[useQuantityControl] respectStockLimits:', shouldRespect, 'product.id:', product.id);
    return shouldRespect;
  }, [product.availability?.preorder, product.id]);
  
  // Check if current quantity exceeds available stock and adjust if needed
  useEffect(() => {
    // Only run this check if we need to respect stock limits
    if (respectStockLimits && typeof product.availability?.stock === 'number') {
      const currentStock = product.availability.stock;
      
      // If current quantity exceeds available stock, adjust it
      if (quantity > currentStock && currentStock > 0) {
        console.log(`[useQuantityControl] Stock reduced: cart has ${quantity} items but only ${currentStock} in stock`);
        
        // Set a small delay to prevent conflicts with other operations
        const timer = setTimeout(() => {
          updateQuantity(currentStock);
        }, 500);
        
        return () => clearTimeout(timer);
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [respectStockLimits, product.availability?.stock, quantity, product.id]);

  useEffect(() => {
    return () => {
      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current);
      }
    };
  }, []);

  // Get the remaining stock (how many more items can be added)
  const remainingStock = useMemo(() => {
    const stock = typeof product.availability?.stock === 'number' ? product.availability.stock : 0;
    console.log('[useQuantityControl] remainingStock:', stock, 'product.id:', product.id);
    return stock;
  }, [product.availability?.stock, product.id]);

  // Check if preorder is null (which means we should respect stock limits)
 

  // Check if we've reached max stock (can't add any more items)
  // Include pendingAdds in the calculation to prevent rapidly exceeding stock
  const isMaxQuantity = useMemo(() => {
    const effectiveRemainingStock = remainingStock - pendingAdds;
    // Only enforce limit if stock limits should be respected
    const maxReached = respectStockLimits && effectiveRemainingStock <= 0;
    
    console.log(
      '[useQuantityControl] isMaxQuantity CALC:',
      { effectiveRemainingStock, respectStockLimits, pendingAdds, maxReached, quantity },
      'product.id:', product.id
    );
    
    return maxReached;
  }, [respectStockLimits, remainingStock, pendingAdds, product.id, quantity]);

  // Message to show about remaining stock
  const stockLimitMessage = useMemo(() => {
    // Account for pending adds in the displayed remaining stock
    const effectiveRemainingStock = Math.max(0, remainingStock - pendingAdds);

    if (respectStockLimits && effectiveRemainingStock <= 5) {
      return `Осталось на складе: ${effectiveRemainingStock} шт.`;
    }
    return null;
  }, [respectStockLimits, remainingStock, pendingAdds, quantity]);

  const updateQuantity = async (newQuantity: number) => {
    if (isLoading) return;
    if (newQuantity < 1) return;

    const changeAmount = newQuantity - quantity;

    // Only block increases that would exceed stock
    if (changeAmount > 0 && respectStockLimits && newQuantity > remainingStock) {
      return;
    }

    // Only track pendingAdds for increases
    if (changeAmount > 0) {
      setPendingAdds(prev => prev + changeAmount);
    }

    const oldQuantity = previousQuantity.current;
    setIsLoading(true);

    try {
      // Optimistically update local state
      setQuantity(newQuantity);
      dispatch(
        changeQuantity({
          id: product.id,
          quantity: newQuantity,
          price: product.price,
        })
      );

      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current);
      }

      debounceTimer.current = setTimeout(async () => {
        try {
          // For authenticated users, we can get price from the cart API response
          // if (isAuthenticated) {
            const result = await sendCart({
              id: product.id,
              quantity: newQuantity,
              selected: product.selected,
            });

            if ('error' in result) {
              // Revert on error
              setQuantity(oldQuantity);
              dispatch(
                changeQuantity({
                  id: product.id,
                  quantity: oldQuantity,
                  price: product.price,
                })
              );
              // Error toast is now handled by the API layer
              // Clear pending adds since we're reverting
              setPendingAdds(0);
              return;
            } else if ('data' in result) {
              // Update with the price from the server response
              console.log('Server response price:', result.data.data.price);

              // Make sure the price is correctly formatted
              const updatedPrice = {
                ...result.data.data.price,
                final: Number(result.data.data.price.final),
                base: Number(result.data.data.price.base),
              };

              console.log('Updated price:', updatedPrice);

              dispatch(
                changeQuantity({
                  id: product.id,
                  quantity: newQuantity,
                  price: updatedPrice,
                })
              );
            }
          // } else {
          //   // For non-authenticated users, we need to get the price separately
          //   const priceResponse = await getItemPrice({
          //     item_id: product.id,
          //     quantity: newQuantity,
          //   });

          //   // Update price if available
          //   if ('data' in priceResponse && priceResponse.data.data) {
          //     console.log('Price response for non-authenticated user:', priceResponse.data.data.price);

          //     // Make sure the price is correctly formatted
          //     const updatedPrice = {
          //       ...priceResponse.data.data.price,
          //       final: Number(priceResponse.data.data.price.final),
          //       base: Number(priceResponse.data.data.price.base),
          //     };

          //     console.log('Updated price for non-authenticated user:', updatedPrice);

          //     dispatch(
          //       changeQuantity({
          //         id: product.id,
          //         quantity: newQuantity,
          //         price: updatedPrice,
          //       })
          //     );
          //   }
          // }

          previousQuantity.current = newQuantity;

          // Reset pending adds after successful API call
          setPendingAdds(0);
        } catch (error) {
          setQuantity(oldQuantity);
          dispatch(
            changeQuantity({
              id: product.id,
              quantity: oldQuantity,
              price: product.price,
            })
          );
          // Error toast is now handled by the API layer
          // Clear pending adds since we're reverting
          setPendingAdds(0);
        }
      }, 500);
    } catch (error) {
      setQuantity(oldQuantity);
      dispatch(
        changeQuantity({
          id: product.id,
          quantity: oldQuantity,
          price: product.price,
        })
      );
      // Error toast is now handled by the API layer
      // Clear pending adds since we're reverting
      setPendingAdds(0);
    } finally {
      setIsLoading(false);
    }
  };

  const handleIncrease = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    // Set loading state immediately for visual feedback
    setIsLoading(true);
    
    try {
      // Only allow increase if there's stock remaining (counting pending adds)
      if (!isMaxQuantity) {
        await updateQuantity(quantity + 1);
      }
    } finally {
      // Ensure loading state is reset even if there's an error
      setTimeout(() => setIsLoading(false), 100);
    }
  };

  const handleDecrease = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    // Set loading state immediately for visual feedback
    setIsLoading(true);
    
    try {
      if (quantity > 1) {
        // For decreases, we don't need to track pending adds
        await updateQuantity(quantity - 1);
      } else if (enableRemove && quantity === 1) {
        if (debounceTimer.current) {
          clearTimeout(debounceTimer.current);
        }

        // Optimistically update UI - this is the key action that updates the Redux store
        dispatch(removeFromCart(product));

        try {
          // if (isAuthenticated) {
            // For authenticated users, send to server
            await sendCart({
              id: product.id,
              quantity: 0,
              selected: product.selected,
            });
          // }
          // No need to dispatch again since we already did it optimistically
        } catch (error) {
          // Error toast is now handled by the API layer
          // If there's an error, we could add the item back to the cart
          dispatch(addToCart({...product, quantity: 1}));
        }
      }
    } finally {
      // Ensure loading state is reset even if there's an error
      setTimeout(() => setIsLoading(false), 100);
    }
  };

  return {
    quantity,
    isLoading: isLoading || sendCartLoading || priceLoading,
    handleIncrease,
    handleDecrease,
    isMinQuantity: !enableRemove && quantity === 1,
    isMaxQuantity,
    stockLimitMessage,
  };
};
