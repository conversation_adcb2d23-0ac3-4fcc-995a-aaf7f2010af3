// src/components/ProductPage/Attributes/ProductAttributeValue.tsx
import React from "react";

import { checkCloseToWhite } from "@/shared/lib";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/shared/ui/tooltip/tooltip";
import type { Product, ProductGroup } from "@/entities/product";
interface AttributeValue {
  value: string | number;
  text: string;
  current: boolean;
  available: boolean;
  color?: string;
  second_color?: string;
}

interface ProductAttributeValueProps {
  id: string | number;
  value: AttributeValue;
  handleChangeAttribute: (event: React.MouseEvent<HTMLDivElement>) => void;
  group?: ProductGroup; // Optional for backward compatibility
}

const ProductAttributeValue: React.FC<ProductAttributeValueProps> = ({ id, value, handleChangeAttribute, group }) => {
  // Find the product variant that matches this specific attribute value
  const findVariantByAttributeValue = (attributeId: string | number, attributeValue: string | number): Product | null => {
    if (!group?.variants) return null;
    
    // Find variant that has this specific attribute value
    const foundVariant = group.variants.find(variant => 
      variant.attributes?.some(attr => 
        attr.id === Number(attributeId) && attr.value === Number(attributeValue)
      )
    );
    
    return foundVariant || null;
  };

  const variantProduct = findVariantByAttributeValue(id, value.value);
  const attributeElement = (
    <div
      data-id={id}
      data-value={value.value}
      data-text={value.text}
      onClick={handleChangeAttribute}
      className={`h-12 p-1 ${
        value.current ? "border-colGreen" : "border-colLightGray"
      } hover:border-colGreen ${
        value.available ? "bg-transparent" : "bg-colLightGray"
      } rounded-[10px] border flex justify-center items-center cursor-pointer`}
    >
      {value.second_color ? (
        <>
          <div
            style={{ backgroundColor: `${value.color}` }}
            className={`w-5 h-10 rounded-l-full ${checkCloseToWhite(value.color) ? 'border border-colLightGray' : ''}`}
          ></div>
          <div
            style={{ backgroundColor: `${value.second_color}` }}
            className={`w-5 h-10 rounded-r-full ${checkCloseToWhite(value.second_color) ? 'border border-colLightGray' : ''}`}
          ></div>
        </>
      ) : value.color ? (
        <div
          style={{ backgroundColor: `${value.color}` }}
          className={`w-10 h-10 rounded-full ${checkCloseToWhite(value.color) ? 'border border-colLightGray' : ''}`}
        ></div>
      ) : (
        value.text
      )}
    </div>
  );

  // Show unavailable tooltip
  if (!value.available) {
    return (
      <Tooltip>
        <TooltipTrigger asChild>
          {attributeElement}
        </TooltipTrigger>
        <TooltipContent
          className='[&>svg]:fill-[#1f1f1f] group/badge'
          isArrow={true}
          arrowClass='fill-[#1f1f1f]'
        >
          <div className='bg-[#1f1f1f] text-white py-1 px-2 rounded-[6px] text-[14px]'>
            Товар недоступен в таком сочетании
          </div>
        </TooltipContent>
      </Tooltip>
    );
  }

  // Show color tooltip for color attributes
  if (value.color) {
    return (
      <Tooltip>
        <TooltipTrigger asChild>
          {attributeElement}
        </TooltipTrigger>
        <TooltipContent
          className='[&>svg]:fill-[#1f1f1f] group/badge hidden lg:block'
          isArrow={true}
          arrowClass='fill-[#1f1f1f]'
        >
          <div className="bg-[#1f1f1f] rounded-[10px] overflow-hidden">
            <div className="w-[80px] h-[80px] p-3 box-content">
              {value.second_color ? (
                <>
                  <div
                    style={{ backgroundColor: `${value.color}` }}
                    className={`w-1/2 h-full inline-block rounded-l`}
                  ></div>
                  <div
                    style={{ backgroundColor: `${value.second_color}` }}
                    className={`w-1/2 h-full inline-block rounded-r`}
                  ></div> 
                </>
              ) : (
                <div
                  style={{ backgroundColor: `${value.color}` }}
                  className={`w-full h-full rounded`}
                ></div>
              )}
            </div>
            {/* Color name and price info */}
            <div className="px-3 pb-3 text-white text-sm">
              <div className="font-medium">{value.text}</div>
              {variantProduct?.price && (
                <div className="text-xs opacity-80 mt-1">
                  {variantProduct.price.final} {variantProduct.price.currency?.symbol || variantProduct.price.currency?.code}
                </div>
              )}
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    );
  }

  // Show tooltip for text attributes that are available (if they have variant price info)
  if (value.available && variantProduct?.price) {
    return (
      <Tooltip>
        <TooltipTrigger asChild>
          {attributeElement}
        </TooltipTrigger>
        <TooltipContent
          className='[&>svg]:fill-[#1f1f1f] group/badge'
          isArrow={true}
          arrowClass='fill-[#1f1f1f]'
        >
          <div className='bg-[#1f1f1f] text-white py-2 px-3 rounded-[6px] text-sm'>
            <div className="font-medium">{value.text}</div>
            <div className="text-xs opacity-80 mt-1">
              {variantProduct.price.final} {variantProduct.price.currency?.symbol || variantProduct.price.currency?.code}
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    );
  }

  // No tooltip for text attributes without price info
  return attributeElement;
}

export default ProductAttributeValue;
