import { Navigation } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';

import { useGetMainPageDataQuery } from '@/redux/api/contentEndpoints';
import { ProductCard } from '@/widgets/product-card';

export const SaleProducts = () => {
  const { data, isLoading, isSuccess } = useGetMainPageDataQuery();

  return (
    <div className="py-5 slider">
      <h1 className="text-colBlack text-2xl mm:text-4xl font-semibold pb-4">
        Со скидкой
      </h1>
      <div className="">
        <Swiper
         modules={[Navigation]}
        navigation={window.innerWidth >= 576}
        spaceBetween={16}
        autoHeight={true}
        breakpoints={{
          260: {
            slidesPerView: 2,
          },
          768: {
            slidesPerView: 3,
          },
          992: {
            slidesPerView: 4,
          },
          1200: {
            slidesPerView: 5,
          },
        }}
        >
          {isSuccess
            ? data?.withDiscount?.map((el) => (
                <SwiperSlide key={el?.id}>
                  <ProductCard product={el} className="min-h-[480px]"/>
                </SwiperSlide>
              ))
            : null}
        </Swiper>
      </div>
    </div>
  );
};
