
import { createBrowserRouter, createRoutesFromElements, Route } from 'react-router-dom';

import { Layout } from '@/app/layouts/Layout';
import { CatalogRoot } from '@/components/Catalog/CatalogRoot';
import { CatalogTest } from '@/components/Catalog/CatalogTest';
import { ExplorePage } from '@/components/Catalog/ExplorePage';
import { SearchTest } from '@/components/Catalog/SearchTest';
import { ChangePasswordForm } from '@/features/change-password';
import MyOrders from '@/components/Profile/MyOrders/MyOrders';
import Organizations from '@/components/Profile/Organizations/Organizations';
import PersonalData from '@/components/Profile/PersonalData/PersonalData';
import UserReviews from '@/components/Profile/UserReviews/UserReviews';
import ProtectedRoute from '@/features/auth/guard/ProtectedRoute';
import AboutUsPage from '@/pages/AboutUsPage';
import { CartPage } from '@/pages/cart/CartPage';
import CheckoutPage from '@/pages/CheckoutPage';
import ComparisonPage from '@/pages/ComparisonPage';
import ContactsPage from '@/pages/ContactsPage';
import FavoritesPage from '@/pages/FavoritesPage';


import Home from '@/pages/Home/Home';
import PageNotFound from '@/pages/PageNotFound';
import PaymentPage from '@/pages/PaymentPage'; // Refactored: Was PaymentDelivery
import DeliveryPage from '@/pages/DeliveryPage'; // Refactored: Split from PaymentDelivery
import ProductPage from '@/pages/ProductPage/ProductPage';
import Profile from '@/pages/Profile/Profile';
import Wallet from '@/pages/Profile/Wallet';
import ReviewsPage from '@/pages/ReviewsPage'; // General reviews page
import FAQPage from '@/pages/FAQPage'; // For the uncommented FAQ route
import Warranty from '@/pages/Warranty/Warranty';
import WholesalePage from '@/pages/WholesalePage'; // Refactored: Was Wholesale from '@/pages/Wholesale/Wholesale'

export const router = createBrowserRouter(
  createRoutesFromElements(
    <Route>
      <Route path='/' element={<Layout />}>
        <Route index element={<Home />} />
        <Route path='shopping-cart' element={<CartPage />} />
        <Route path='checkout' element={<CheckoutPage />} />
        <Route path='favorites' element={<FavoritesPage />} />
        <Route path='comparison' element={<ComparisonPage />} />

        {/* Search routes */}
        <Route path='search' element={<SearchTest />} />
        {/* <Route path='search' element={<SearchProducts />} /> */}

        {/* Catalog routes - Category pages */}
        <Route path='catalog'>
          <Route index element={<CatalogRoot />} />
          <Route path=':categoryId'>
            <Route
              index
              element={
                <>
                  {/* <Catalog /> */}
                  <CatalogTest />
                </>
              }
            />
            <Route path=':productId' element={<ProductPage />} />
            <Route path=':productId/reviews' element={<ReviewsPage />} />
          </Route>
        </Route>

        {/* Brand pages - Query parameter approach */}
        <Route path='brands/:brandId' element={<ExplorePage />}>
          {/* <Route path='brands' element={<BrandProducts />}> */}
          {/* No nested routes here to avoid conflict */}
        </Route>

        {/* Tag pages - Query parameter approach */}
        <Route path='tags/:tagId' element={<ExplorePage />}>
          {/* <Route path='tags' element={<TagProducts />}> */}
          {/* No nested routes here to avoid conflict */}
        </Route>
        <Route path='profile/orders/:orderId' element={<CheckoutPage />} />

        <Route
          path='profile'
          element={
            <ProtectedRoute>
              <Profile />
            </ProtectedRoute>
          }
        >
          <Route path='personal-data' element={<PersonalData />} />
          <Route path='organizations' element={<Organizations />} />
          <Route path='change-password' element={<ChangePasswordForm />} />
          <Route path='orders'>
            <Route index element={<MyOrders />} />
          </Route>
          <Route path='wallet' element={<Wallet />} />
          <Route path='user-reviews' element={<UserReviews />} />
        </Route>
        <Route path='payment' element={<PaymentPage />} />
        <Route path='delivery' element={<DeliveryPage />} />
        <Route path='warranty' element={<Warranty />} />
        <Route path='wholesale' element={<WholesalePage />} />
        <Route path='contacts' element={<ContactsPage />} />
        <Route path='about' element={<AboutUsPage />} />
        <Route path='faq' element={<FAQPage />} />
        <Route path='*' element={<PageNotFound />} />
      </Route>
    </Route>
  )
);
