// src/features/auth/guard/ProtectedRoute.tsx
// NOTE: Ideally, this would be in a 'guard' subfolder (src/features/auth/guard/ProtectedRoute.tsx)
import type React from 'react';
import { Navigate, useLocation } from 'react-router-dom';

import { Loading } from '@/shared/ui/Loader';
import { useState, useEffect } from 'react';
interface ProtectedRouteProps {
  children: React.ReactNode;
}

/**
 * A component to guard routes, ensuring the user is authenticated.
 * It handles three states:
 * 1. Loading: While authentication status is being determined from the server.
 * 2. Authenticated: Renders the protected content.
 * 3. Not Authenticated: Redirects to the home page, passing the intended location.
 */
const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const location = useLocation();
  const [isLoading, setIsLoading] = useState(true);
  const [accessGranted, setAccessGranted] = useState(false);
  const [redirectUrl, setRedirectUrl] = useState<string | null>(null);
  const [fetchError, setFetchError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchRouteInfo = async () => {
      // Reset state for new navigation
      setIsLoading(true);
      setRedirectUrl(null);
      setFetchError(null);
      setAccessGranted(false);

      const path = location.pathname + location.search;
      console.log(`[ProtectedRoute] Fetching /route/info for path: ${path}`);

      try {
        const response = await fetch(`${API_BASE_URL}/route/info?url=${encodeURIComponent(path)}`, {
          credentials: 'include',
        });

        if (!response.ok) {
          throw new Error(`Network response was not ok: ${response.statusText}`);
        }

        const data: RouteInfoResponse = await response.json();
        console.log('[ProtectedRoute] /route/info response:', data);

        if (data.access_granted) {
          setAccessGranted(true);
        } else if (data.error?.redirect_url) {
          setRedirectUrl(data.error.redirect_url);
        } else if (data.redirect?.url) {
          setRedirectUrl(data.redirect.url);
        } else {
          // Default redirect if access is denied and no specific URL is given
          setRedirectUrl('/');
        }
      } catch (error) {
        console.error('[ProtectedRoute] Error fetching /route/info:', error);
        setFetchError(error as Error);
        setRedirectUrl('/'); // Default to home on fetch error
      } finally {
        setIsLoading(false);
      }
    };

    fetchRouteInfo();
  }, [location.pathname, location.search]);

  if (isLoading) {
    return <Loading />;
  }

  if (fetchError) {
    // The redirectUrl is already set to '/' in the catch block
    return <Navigate to={redirectUrl!} state={{ from: location, error: 'route_info_failed' }} replace />;
  }

  if (accessGranted) {
    return <>{children}</>;
  }

  if (redirectUrl) {
    // If redirecting to home, keep the 'from' state so the login modal can trigger
    const navState = redirectUrl === '/' ? { from: location } : undefined;
    return <Navigate to={redirectUrl} state={navState} replace />;
  }

  // Fallback, should not be reached if logic is correct
  return <Navigate to="/" replace />;
};

export default ProtectedRoute;
