import React, { useState, forwardRef, useImperativeHandle, useRef, useEffect } from 'react';
import { scrollToRef } from '@/shared/lib/scrollToTop';

import CharactersticsTab from '../ProductTabs/CharactersticsTab';
import FilesTab from '../ProductTabs/FilesTab';
import ReviewsTab from '../ProductTabs/ReviewsTab';
import InfoTab from '../ProductTabs/InfoTab';

interface TabItem {
  id: string;
  label: string;
  notification?: number;
}

interface AnimatedTabsProps {
  current?: any;
  group?: any;
  className?: string;
}

export interface AnimatedTabsRef {
  switchToTab: (index: number) => void;
}

const tabs: TabItem[] = [
  { id: 'characteristics', label: 'Характеристика и описание' },
  { id: 'files', label: 'Документы и сертификаты' },
  { id: 'reviews', label: 'Отзывы' },
  { id: 'delivery', label: 'Доставка и оплата' },
];

export const AnimatedTabs = forwardRef<AnimatedTabsRef, AnimatedTabsProps>(
  ({ current, group, className = '' }, ref) => {
    const [activeTab, setActiveTab] = useState(0);
    const [gliderStyle, setGliderStyle] = useState({ width: 0, transform: 'translateX(0px)' });
    const tabsContainerRef = useRef<HTMLDivElement>(null);
    const tabRefs = useRef<(HTMLLabelElement | null)[]>([]);

    useImperativeHandle(ref, () => ({
      switchToTab: (index: number) => {
        setActiveTab(index);
        updateGliderPosition(index);
        // Scroll to tabs with a small delay for smooth UX
        setTimeout(() => {
          scrollToRef(tabsContainerRef, 100);
        }, 50);
      },
    }));

    const updateGliderPosition = (index: number) => {
      const selectedTab = tabRefs.current[index];
      if (selectedTab) {
        const { offsetLeft, offsetWidth } = selectedTab;
        setGliderStyle({
          width: offsetWidth,
          transform: `translateX(${offsetLeft}px)`,
        });
      }
    };

    const handleTabChange = (index: number) => {
      setActiveTab(index);
      updateGliderPosition(index);
    };

    // Update glider position on mount and when activeTab changes
    useEffect(() => {
      // Small delay to ensure DOM is ready
      const timer = setTimeout(() => {
        updateGliderPosition(activeTab);
      }, 10);

      return () => clearTimeout(timer);
    }, [activeTab]);

    // Update glider position on window resize
    useEffect(() => {
      const handleResize = () => {
        updateGliderPosition(activeTab);
      };

      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }, [activeTab]);

    return (
      <div className={`${className}`} ref={tabsContainerRef}>
        {/* Animated Tab Navigation */}
          <div className="relative flex bg-colLightGray rounded-lg p-3 overflow-hidden  w-full">
            {tabs.map((tab, index) => (
              <div key={tab.id} className='basis-[calc(25%-40px/4)]'>
                <input
                  type="radio"
                  id={`tab-${index}`}
                  name="product-tabs"
                  checked={activeTab === index}
                  onChange={() => handleTabChange(index)}
                  className="hidden"
                />
                <label
                  ref={(el) => (tabRefs.current[index] = el)}
                  htmlFor={`tab-${index}`}
                  className={`
                    relative z-20 flex items-center justify-center
                    h-[54px] px-4 sm:px-6 min-w-[160px] sm:min-w-[200px]
                    text-sm sm:text-base font-medium text-center
                    rounded-full cursor-pointer transition-colors duration-150 ease-in
                    ${activeTab === index
                      ? 'text-white'
                      : 'text-colDarkGray hover:text-colGreen'
                    }
                  `}
                >
                  <span className="truncate">{tab.label}</span>
                  {tab.notification && (
                    <span className={`
                      flex items-center justify-center w-8 h-8 ml-2 sm:ml-3 rounded-full
                      text-xs sm:text-sm font-medium transition-colors duration-150 ease-in flex-shrink-0
                      ${activeTab === index
                        ? 'bg-colGreen text-white'
                        : 'bg-colLightGray text-colDarkGray'
                      }
                    `}>
                      {tab.notification}
                    </span>
                  )}
                </label>
              </div>
            ))}

            {/* Glider */}
            <div
              className="absolute top-3 left-3 h-[54px] bg-colGreen rounded-lg transition-all duration-300 ease-out z-10"
              style={{
                width: `${gliderStyle.width}px`,
                transform: gliderStyle.transform,
              }}
            />
        </div>

        {/* Tab Content */}
        <div className="mt-8">
          {activeTab === 0 && (
            <div>
              <CharactersticsTab current={current} group={group} />
            </div>
          )}
          {activeTab === 1 && (
            <div>
              <FilesTab product={group} />
            </div>
          )}
          {activeTab === 2 && (
            <div>
              <ReviewsTab current={current} />
            </div>
          )}
          {activeTab === 3 && (
            <div>
              <InfoTab />
            </div>
          )}
        </div>
      </div>
    );
  }
);

AnimatedTabs.displayName = 'AnimatedTabs';
