import React, { useState, forwardRef, useImperativeHandle, useRef, useEffect } from 'react';
import { scrollToRef } from '@/shared/lib/scrollToTop';

import CharactersticsTab from '../ProductTabs/CharactersticsTab';
import FilesTab from '../ProductTabs/FilesTab';
import ReviewsTab from '../ProductTabs/ReviewsTab';
import InfoTab from '../ProductTabs/InfoTab';

interface TabItem {
  id: string;
  label: string;
  notification?: number;
}

interface AnimatedTabsProps {
  current?: any;
  group?: any;
  className?: string;
}

export interface AnimatedTabsRef {
  switchToTab: (index: number) => void;
}

const tabs: TabItem[] = [
  { id: 'characteristics', label: 'Характеристика и описание' },
  { id: 'files', label: 'Документы и сертификаты' },
  { id: 'reviews', label: 'Отзывы' },
  { id: 'delivery', label: 'Доставка и оплата' },
];

export const AnimatedTabs = forwardRef<AnimatedTabsRef, AnimatedTabsProps>(
  ({ current, group, className = '' }, ref) => {
    const [activeTab, setActiveTab] = useState(0);
    const tabsContainerRef = useRef<HTMLDivElement>(null);

    useImperativeHandle(ref, () => ({
      switchToTab: (index: number) => {
        setActiveTab(index);
        // Scroll to tabs with a small delay for smooth UX
        setTimeout(() => {
          scrollToRef(tabsContainerRef, 100);
        }, 50);
      },
    }));

    const handleTabChange = (index: number) => {
      setActiveTab(index);
    };

    return (
      <div className={`w-full ${className}`} ref={tabsContainerRef}>
        {/* Animated Tab Navigation */}
        <div className="flex justify-center mb-8">
          <div className="relative flex bg-white rounded-full p-3 shadow-[0_0_1px_0_rgba(24,94,224,0.15),_0_6px_12px_0_rgba(24,94,224,0.15)] overflow-hidden">
            {tabs.map((tab, index) => (
              <React.Fragment key={tab.id}>
                <input
                  type="radio"
                  id={`tab-${index}`}
                  name="product-tabs"
                  checked={activeTab === index}
                  onChange={() => handleTabChange(index)}
                  className="hidden"
                />
                <label
                  htmlFor={`tab-${index}`}
                  className={`
                    relative z-20 flex items-center justify-center
                    h-[54px] px-4 sm:px-6 min-w-[160px] sm:min-w-[200px]
                    text-sm sm:text-base font-medium text-center
                    rounded-full cursor-pointer transition-colors duration-150 ease-in
                    ${activeTab === index
                      ? 'text-colGreen'
                      : 'text-colDarkGray hover:text-colGreen'
                    }
                  `}
                >
                  <span className="truncate">{tab.label}</span>
                  {tab.notification && (
                    <span className={`
                      flex items-center justify-center w-8 h-8 ml-2 sm:ml-3 rounded-full
                      text-xs sm:text-sm font-medium transition-colors duration-150 ease-in flex-shrink-0
                      ${activeTab === index
                        ? 'bg-colGreen text-white'
                        : 'bg-colLightGray text-colDarkGray'
                      }
                    `}>
                      {tab.notification}
                    </span>
                  )}
                </label>
              </React.Fragment>
            ))}

            {/* Glider */}
            <div
              className={`
                absolute top-3 left-3 h-[54px] bg-colLightGray
                rounded-full transition-transform duration-300 ease-out z-10
              `}
              style={{
                width: `calc(${100 / tabs.length}% - ${12 / tabs.length}px)`,
                transform: `translateX(${activeTab * 100}%)`,
              }}
            />
          </div>
        </div>

        {/* Tab Content */}
        <div className="mt-8">
          {activeTab === 0 && (
            <div>
              <CharactersticsTab current={current} group={group} />
            </div>
          )}
          {activeTab === 1 && (
            <div>
              <FilesTab product={group} />
            </div>
          )}
          {activeTab === 2 && (
            <div>
              <ReviewsTab current={current} />
            </div>
          )}
          {activeTab === 3 && (
            <div>
              <InfoTab />
            </div>
          )}
        </div>
      </div>
    );
  }
);

AnimatedTabs.displayName = 'AnimatedTabs';
