import { Skeleton } from '@/shared/ui/skeleton';

interface ProductCardSkeletonProps {
  className?: string;
}

export const ProductCardSkeleton = ({ className = '' }: ProductCardSkeletonProps) => {
  return (
    <div className={`sm:min-w-[220px] flex flex-col gap-2 justify-between ${className}`}>
      {/* Image skeleton - matches ProductCard PreviewGallery */}
      <Skeleton className="w-full min-h-[225px] max-h-[225px] rounded-lg" />

      {/* Badges skeleton - matches DiscountBadge and StockStatus */}
      <div className="flex gap-2">
        <Skeleton className="h-6 w-16 rounded-md" />
        <Skeleton className="h-6 w-20 rounded-md" />
      </div>

      {/* Price skeleton - matches PriceDisplay */}
      <div className="flex flex-col gap-1">
        <Skeleton className="h-7 w-24 rounded-md" />
        <Skeleton className="h-4 w-16 rounded-md" />
      </div>

      {/* Product tags skeleton - matches ProductTags */}
      <Skeleton className="h-6 w-20 rounded-full" />

      {/* Product name skeleton - matches ProductName with 2 lines */}
      <div className="flex flex-col gap-1 mt-1">
        <Skeleton className="h-5 w-full rounded-md" />
        <Skeleton className="h-5 w-3/4 rounded-md" />
      </div>

      {/* Cart button skeleton - matches AddToCartButton */}
      <Skeleton className="h-12 w-full rounded-lg mt-auto" />
    </div>
  );
};
