import type { Product } from '@/entities/product';
import { Icon } from '@iconify-icon/react';

interface StockStatusProps {
  product: Product;
  className?: string;
}

export const StockStatus = ({ product, className = '' }: StockStatusProps) => {
  return (
    <div className={`flex  gap-1 ${className}`}>
      <div
        className={`py-0.5 px-2 bg-colLightGray rounded-lg text-xs font-medium  w-fit`}
      >
        {product.availability?.stock === 0
          ? 'нет в наличии'
          : `в наличии: ${product.availability?.stock}`}
      </div>
      {product.availability?.preorder !== null ? (
        <div
          className={`py-0.5 px-2 bg-colLightGray flex rounded-lg items-center gap-1 text-xs font-medium w-fit`}
        >
          <div>предзаказ</div>
         
         <Icon icon="solar:check-circle-bold" width="14" height="14" className="text-colGreen"/>
        </div>
      ) : null}
    </div>
  );
};
