import { useGetCategoryTreeQuery } from '@/entities/category';
import { useGetBasicFiltersQuery } from '@/entities/filter';
import { useEffect, useRef, useState } from 'react';
import { NavLink } from 'react-router-dom';

export const CatalogFastAccess = () => {
  const { data } = useGetCategoryTreeQuery();
  const { data: basicFilters } = useGetBasicFiltersQuery();
  const scrollableDivRef = useRef<HTMLDivElement>(null);
  const [isScrolling, setIsScrolling] = useState(false);
  const scrollAnimationRef = useRef<number | null>(null);

  // Configuration for auto-scrolling
  const SCROLL_THRESHOLD = 60; // pixels from edge to trigger scrolling
  const MAX_SCROLL_SPEED = 12; // maximum scrolling speed
  const SCROLL_ACCELERATION = 0.15; // how quickly scrolling accelerates

  // Handle mouse wheel scrolling (horizontal)
  useEffect(() => {
    const handleWheel = (event: WheelEvent) => {
      const div = scrollableDivRef.current;
      if (div && event.deltaY !== 0) {
        // Prevent default vertical scroll
        event.preventDefault();
        // Scroll horizontally instead
        div.scrollLeft += event.deltaY;
      }
    };

    const div = scrollableDivRef.current;
    if (div) {
      div.addEventListener('wheel', handleWheel);
    }

    return () => {
      if (div) {
        div.removeEventListener('wheel', handleWheel);
      }
    };
  }, []);

  // Auto-scroll when mouse is near the edges
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const div = scrollableDivRef.current;
      if (!div) return;

      // Get div's bounding rectangle
      const rect = div.getBoundingClientRect();

      // Check if mouse is inside the scrollable div
      if (
        e.clientY >= rect.top &&
        e.clientY <= rect.bottom &&
        e.clientX >= rect.left &&
        e.clientX <= rect.right
      ) {
        // Mouse is within the div
        setIsScrolling(true);

        // Calculate distance from left edge
        const distanceFromLeft = e.clientX - rect.left;
        // Calculate distance from right edge
        const distanceFromRight = rect.right - e.clientX;

        // Determine scroll direction and speed
        let scrollSpeed = 0;

        if (distanceFromLeft < SCROLL_THRESHOLD) {
          // Mouse is near left edge, scroll left
          scrollSpeed = -Math.min(
            MAX_SCROLL_SPEED,
            (SCROLL_THRESHOLD - distanceFromLeft) * SCROLL_ACCELERATION
          );
        } else if (distanceFromRight < SCROLL_THRESHOLD) {
          // Mouse is near right edge, scroll right
          scrollSpeed = Math.min(
            MAX_SCROLL_SPEED,
            (SCROLL_THRESHOLD - distanceFromRight) * SCROLL_ACCELERATION
          );
        }

        // Update scrollLeft if we have a non-zero scroll speed
        if (scrollSpeed !== 0) {
          if (scrollAnimationRef.current) {
            cancelAnimationFrame(scrollAnimationRef.current);
          }

          const scrollStep = () => {
            if (div) {
              div.scrollLeft += scrollSpeed;
              scrollAnimationRef.current = requestAnimationFrame(scrollStep);
            }
          };

          scrollAnimationRef.current = requestAnimationFrame(scrollStep);
        } else if (scrollAnimationRef.current) {
          // Stop scrolling if mouse is not near edges
          cancelAnimationFrame(scrollAnimationRef.current);
          scrollAnimationRef.current = null;
        }
      } else if (isScrolling) {
        // Mouse left the div, stop scrolling
        setIsScrolling(false);
        if (scrollAnimationRef.current) {
          cancelAnimationFrame(scrollAnimationRef.current);
          scrollAnimationRef.current = null;
        }
      }
    };

    // Add mousemove event listener
    document.addEventListener('mousemove', handleMouseMove);

    // Clean up
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      if (scrollAnimationRef.current) {
        cancelAnimationFrame(scrollAnimationRef.current);
      }
    };
  }, [isScrolling]);

  return (
    <div className='flex flex-col'>
      <div
        className='content mx-auto flex items-center scrollable overflow-x-scroll gap-2 py-2'
        ref={scrollableDivRef}
      >
        {basicFilters?.tags?.map((tag) => (
          <NavLink
            to={`tags/${tag?.tag}`}
            key={`basic-filter-${tag?.tag}`}
            style={{ backgroundColor: `${tag?.background_color}` }}
            className='rounded h-[27px] flex items-center justify-center px-4'
          >
            {tag?.light_icon ? (
              <img src={tag?.light_icon?.medium} className='w-4 h-4' alt='*' />
            ) : null}

            <span
              style={{ color: tag?.text_color }}
              className='text-sm font-semibold text-white pl-1'
            >
              {tag?.tag}
            </span>
          </NavLink>
        ))}
        {data?.children?.slice(0, 14)?.map((el) => (
          <NavLink
            to={`catalog/${el.slug}`}
            state={{ category: el }}
            key={el?.id}
            className='whitespace-nowrap text-colBlack text-sm font-semibold'
          >
            {el?.name}
          </NavLink>
        ))}
        <NavLink to='/catalog' className='whitespace-nowrap text-colGreen text-sm font-semibold'>
          Показать все
        </NavLink>
      </div>
    </div>
  );
};
