import React from 'react';
import { NavLink } from 'react-router-dom';


// import useInitialDataFetch from '@/hooks/useInitialDataFetch'; // Not currently used
// import logo from '@/shared/assets/images/logo.svg'; // Not currently used
import { useDevice } from '@/shared/lib/hooks/useDevice';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/app/store';
import { closeCatalog, toggleCatalog } from '@/app/store/ui/uiSlice';

import { CatalogFastAccess } from './CatalogFastAccess';
import { HeaderControls } from './HeaderControls/HeaderControls';
import { PreHeader } from './PreHeader';
import { SearchBar } from '@/features/search';
import { MobileHeader } from './MobileHeader'; // Import MobileHeader
import { Icon } from '@iconify-icon/react';



export const Header: React.FC = () => {
    const { isMobile } = useDevice();
  const dispatch = useDispatch();
  const showCatalog = useSelector((state: RootState) => state.ui.isCatalogVisible);

  // useInitialDataFetch(); // Not currently used

  return (
    <>
      {/* Step 1: Render the correct header bar based on device */}
      {isMobile ? (
        <MobileHeader />
      ) : (
        <>
          <PreHeader />
          {/* Desktop sticky header part */}
          <div className="mx-auto sticky top-0 py-3 lg:space-x-5 bg-white z-[40] w-full lining-nums proportional-nums">
            <div className="content flex justify-between items-center">
              <NavLink
                onClick={() => dispatch(closeCatalog())}
                className="min-w-[90px] w-[90px] hidden lg:block"
                to="/"
              >
                <span className="text-xl font-bold text-colGreen">FURNICA</span>
              </NavLink>
              <button
                onClick={() => dispatch(toggleCatalog())}
                className="bg-colGreen text-white flex justify-center items-center max-w-[40px] mm:max-w-[140px] w-full min-h-[40px] rounded mr-3 lg:mr-0"
              >
                {showCatalog ? (
                  <Icon icon='material-symbols:close-rounded' width={28} height={28}/>
                ) : (
                  <Icon icon='solar:hamburger-menu-outline' width={28} height={28}/>
                )}
                <span className="hidden mm:block ml-2">Каталог</span>
              </button>
              <div className="flex-grow h-[42px] flex items-center">
                <SearchBar />
              </div>
              <HeaderControls />
            </div>
          </div>
          <CatalogFastAccess />
        </>
      )}

    </>
  );
};
