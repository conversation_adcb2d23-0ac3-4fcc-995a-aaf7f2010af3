// src/widgets/product-card/ui/ProductCard/ProductCard.tsx
import { NavLink } from 'react-router-dom';

import { ProductTags, type Product } from '@/entities/product';
import { AddToCartButton, QuantityControl } from '@/features/cart';
import {
  useProductCard,
  PreviewGallery,
  PriceDisplay,
  DiscountBadge,
  StockStatus,
  ProductName,
} from '@/widgets/product-card';

interface ProductCardProps {
  product: Product;
  className?: string;
}

export const ProductCard = ({
  product,
  className = ''
}: ProductCardProps): JSX.Element => {
  const { productInCart, productPrice } = useProductCard(product);
  return (
    <NavLink
      to={
        product.slug ? `/catalog/${product.category.slug}/${product.slug}` : ''
      }
      className={`sm:min-w-[220px] overflow-hidden flex flex-col gap-2 justify-between ${className}`}
    >
    {/* <NavLink
      to={
        product.slug ? `/catalog/${product.category.slug}/${product.slug}` : ''
      }
      className="min-h-[435px] min-w-[220px] overflow-hidden flex flex-col gap-2 justify-between"
    > */}
      <div className="flex flex-col gap-2">
        <PreviewGallery
          product={product}
          className="w-full min-h-[225px] max-h-[225px] grow overflow-hidden relative bg-gray-100"
        />
        <div className="flex gap-2">
          <DiscountBadge product={product} />
          <StockStatus product={product} />
        </div>

        {/* Price display - always show the most accurate price available */}
        <PriceDisplay
          price={productPrice}
          variant="total-product-card"
          size="xl"
          showDiscount={true}
        />

        {/* Product tags if available */}
        {product?.tags &&
        Array.isArray(product.tags) &&
        product.tags.length > 0 ? (
          <ProductTags product={product} variant="default" limit={1} />
        ) : null}

        <ProductName
          product={product}
          maxLines={2}
          weight="medium"
          color="text-colText"
          className="mt-1"
        />
      </div>

      {/* Cart controls - AddToCartButton or QuantityControl */}
      {productInCart ? (
        <div className="flex justify-between gap-2">
          <QuantityControl product={productInCart} enableRemove />
        </div>
      ) : (
        <AddToCartButton product={product} />
      )}
    </NavLink>
  );
};
