export const scrollToTop = () => {
  // setTimeout(() => {
  //   window.scrollTo({
  //     top: 0,
  //     behavior: 'auto',
  //   });
  // }, 500);
  window.scrollTo({
    top: 0,
    behavior: 'instant',
  });
};

export const scrollToElement = (elementId: string, offset: number = 0) => {
  const element = document.getElementById(elementId);
  if (element) {
    const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
    const offsetPosition = elementPosition - offset;

    window.scrollTo({
      top: offsetPosition,
      behavior: 'smooth',
    });
  }
};

export const scrollToRef = (ref: React.RefObject<HTMLElement>, offset: number = 0) => {
  if (ref.current) {
    const elementPosition = ref.current.getBoundingClientRect().top + window.pageYOffset;
    const offsetPosition = elementPosition - offset;

    window.scrollTo({
      top: offsetPosition,
      behavior: 'smooth',
    });
  }
};
