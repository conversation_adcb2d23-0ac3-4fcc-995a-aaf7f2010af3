import { Skeleton } from '@/shared/ui/skeleton';

interface ProductCardLineSmallSkeletonProps {
  className?: string;
}

export const ProductCardLineSmallSkeleton = ({
  className = ''
}: ProductCardLineSmallSkeletonProps) => {
  return (
    <div className={`flex gap-5 ${className}`}>
      {/* Product image skeleton */}
      <Skeleton className="h-20 w-20 rounded-lg flex-shrink-0" />

      {/* Product info skeleton */}
      <div className="flex-1 space-y-2">
        {/* Product name skeleton */}
        <Skeleton className="h-6 w-full max-w-[220px] rounded-md" />

        {/* Product description skeleton */}
        <Skeleton className="h-4 w-full max-w-[500px] rounded-md" />

        {/* Product attributes skeleton */}
        <div className="flex gap-3">
          <Skeleton className="h-3 w-[140px] rounded-md" />
          <Skeleton className="h-3 w-[130px] rounded-md" />
          <Skeleton className="h-3 w-[180px] rounded-md" />
        </div>
      </div>
    </div>
  );
};
