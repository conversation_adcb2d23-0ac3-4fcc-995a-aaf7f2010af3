import { Skeleton } from '@/shared/ui/skeleton';

interface ProductCardLineSkeletonProps {
  className?: string;
}

export const ProductCardLineSkeleton = ({
  className = ''
}: ProductCardLineSkeletonProps) => {
  return (
    <div className={`lg:flex justify-between bg-white rounded-lg shadow-sm p-3 ${className}`}>
      {/* Product image skeleton - matches ProductCardLine layout */}
      <div className="mm:flex lg:pr-4 lg:max-w-[800px] w-full">
        <div className="relative">
          <Skeleton className="max-w-[220px] min-w-[220px] h-[220px] rounded-lg" />
        </div>

        {/* Product info skeleton */}
        <div className="flex-1 mm:pl-5 space-y-3 mt-3 mm:mt-0">
          {/* Product name */}
          <Skeleton className="h-6 w-[240px] rounded-md" />

          {/* Product description lines */}
          <div className="space-y-2">
            <Skeleton className="h-4 w-[300px] rounded-md" />
            <Skeleton className="h-4 w-[260px] rounded-md" />
            <Skeleton className="h-4 w-[200px] rounded-md" />
          </div>

          {/* Product attributes */}
          <div className="space-y-1">
            <Skeleton className="h-3 w-[120px] rounded-md" />
            <Skeleton className="h-3 w-[150px] rounded-md" />
            <Skeleton className="h-3 w-[100px] rounded-md" />
          </div>
        </div>
      </div>

      {/* Price and controls skeleton - matches ProductCardLine right side */}
      <div className="lg:min-w-[300px] flex flex-col justify-between mt-4 lg:mt-0">
        {/* Price skeleton */}
        <div className="space-y-2">
          <Skeleton className="h-8 w-[120px] rounded-md" />
          <Skeleton className="h-4 w-[80px] rounded-md" />
        </div>

        {/* Controls skeleton */}
        <div className="flex gap-2 mt-4">
          <Skeleton className="h-12 flex-1 rounded-lg" />
          <Skeleton className="h-12 w-12 rounded-lg" />
          <Skeleton className="h-12 w-12 rounded-lg" />
        </div>
      </div>
    </div>
  );
};
