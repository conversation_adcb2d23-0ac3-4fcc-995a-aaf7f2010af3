import { Icon } from '@iconify-icon/react';
import { NavLink } from 'react-router-dom';

import { useAuthContext, useGetUserDataQuery } from '@/entities/user';
import { useQuantities } from '@/widgets/header';

import { CartButton } from './CartButton';
import LoginButton from './LoginButton';
import ProfileButton from './ProfileButton';

export const HeaderControls = () => {
  const { isAuthenticated, user } = useAuthContext();
  // const token = useSelector((state) => state.user.token);

  // const {
  //   data: user,
  //   isLoading,
  //   refetch,
  // } = useGetUserDataQuery(undefined, { skip: !token });

  const { getFavoritesCount, getComparisonCount, getCartQuantity } =
    useQuantities();

  // useEffect(() => {
  //   if (token) {
  //     refetch();
  //   }
  // }, [token, refetch]);

  return (
    <div className="hidden lg:flex justify-between gap-2">
      <NavLink to="/profile/orders" className="min-w-[70px]">
        {({ isActive }) => (
          <>
            <div className="flex flex-col">
              <Icon
                icon={isActive ? 'solar:box-bold' : 'solar:box-broken'}
                width={24}
                height={24}
                className={isActive ? 'text-colGreen' : 'text-colBlack'}
              />
            </div>
            <span className="text-xs pt-1 font-medium text-colBlack line-clamp-1 text-center">
              Заказы
            </span>
          </>
        )}
      </NavLink>
      <NavLink to="/comparison" className="min-w-[70px]">
        {({ isActive }) => (
          <div className="relative flex flex-col">
            <Icon
              icon={isActive ? 'solar:chart-2-bold' : 'solar:chart-2-linear'}
              width={24}
              height={24}
              className={isActive ? 'text-colGreen' : 'text-colBlack'}
            />

            <span className="text-xs pt-1 font-medium text-colBlack text-center">
              Сравнение
            </span>
            {getComparisonCount() > 0 ? (
              <span className="absolute -top-2 right-0 bg-colGreen h-5  min-w-[20px] flex justify-center items-center text-xs text-white rounded-full px-1 -z-10 lining-nums proportional-nums">
                {getComparisonCount() > 99 ? '99+' : getComparisonCount()}
              </span>
            ) : null}
          </div>
        )}
      </NavLink>
      <NavLink to="/favorites" className="min-w-[70px]">
        {({ isActive }) => (
          <div className="relative flex flex-col">
            <Icon
              icon={isActive ? 'solar:heart-bold' : 'solar:heart-linear'}
              width={24}
              height={24}
              className={isActive ? 'text-colGreen' : 'text-colBlack'}
            />
            <span className="text-xs pt-1 font-medium text-colBlack text-center">
              Избранное
            </span>
            {getFavoritesCount() > 0 ? (
              <span className="absolute -top-2 right-0 bg-colGreen h-5  min-w-[20px] flex justify-center items-center text-xs text-white rounded-full px-1 -z-10">
                {getFavoritesCount() > 99 ? '99+' : getFavoritesCount()}
              </span>
            ) : null}
          </div>
        )}
      </NavLink>
      <CartButton getCartQuantity={getCartQuantity} />
      {isAuthenticated ? <ProfileButton name={user?.name} /> : <LoginButton />}
    </div>
  );
};
