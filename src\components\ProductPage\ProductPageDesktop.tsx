import type React from 'react';
import { useEffect, useState, useRef } from 'react';

import { Breadcrumbs } from '@/widgets/breadcrumbs';

import ProductAttributesList from './Attributes/ProductAttributesList';
import CharacteristicsList from './CharacteristicsList';
import ProductGallery from './ProductGallery';
import { AnimatedTabs, type AnimatedTabsRef } from './AnimatedTabs';
import RightBar from './RightBar';
import TopControls from './TopControls';

import type { Product } from '@/entities/product/Product';
import type { ProductGroup } from '@/entities/product/ProductGroup/ProductGroup';
import type { AttributesValuesList } from '@hooks/useModificationAttributesManager';
import { Icon } from '@iconify-icon/react';

type ProductPageDesktopProps = {
  group: ProductGroup;
  currentProduct: Product | null;
  attributesList: AttributesValuesList;
  handleChangeAttribute: (event: React.MouseEvent<HTMLDivElement>) => void;
};
export const ProductPageDesktop = ({
  group,
  currentProduct,
  attributesList,
  handleChangeAttribute,
}: ProductPageDesktopProps) => {
  const [displayedProduct, setDisplayedProduct] = useState<Product | null>(
    currentProduct
  );
  const tabsRef = useRef<AnimatedTabsRef>(null);

  useEffect(() => {
    if (currentProduct) {
      setDisplayedProduct(currentProduct);
    }
  }, [currentProduct]);

  // Function to switch to a specific tab and scroll to it
  const switchToTab = (index: number) => {
    tabsRef.current?.switchToTab(index);
  };

  return (
    <div className="content lining-nums proportional-nums">
      <div className="flex justify-between items-center">
        <Breadcrumbs breadcrumbs={group?.category_chain} />
        <TopControls
          product={displayedProduct}
        />
      </div>
     
      <div className="flex flex-wrap pb-5 min-h-[420px] gap-5">
        <div className="basis-[calc(42%-40px/3)] max-w-[calc(42%-40px/3)] justify-center items-center">
          <ProductGallery
            files={displayedProduct?.files}
            product={displayedProduct}
          />
        </div>
        <div className="basis-[calc(33%-40px/3)] flex flex-col gap-4">
         
        <div className="text-xl font-semibold">
          {displayedProduct?.fullName}
        </div>
        <button
                  className="flex items-center font-medium   gap-1 proportional-nums lining-nums"
                  onClick={() => {switchToTab(2)}}
                >
                  <Icon icon="solar:star-bold" width={24} height={24} className="icon-btn-icon"/>
                 <div className='flex gap-1 items-center mt-1'>
                  <span className="">
                    {group?.reviews.rating}
                  </span>
                  <span className='w-1.5 h-1.5 bg-colGreen rounded-full'></span>
                  <span className="">
                    {group?.reviews.total_count_text}
                  </span>
                  </div>
                </button>
          <ProductAttributesList
            current={currentProduct}
            attributesList={attributesList}
            handleChangeAttribute={handleChangeAttribute}
            group={group}
          />
            <CharacteristicsList
              current={currentProduct}
              product={group}
              setTabIndex={switchToTab}
            />
        </div>
        <div className="basis-[calc(25%-40px/3)] basis-full">
          <RightBar product={displayedProduct} />
        </div>
      </div>
      <div className="pb-5 min-h-[420px] gap-5">
        <AnimatedTabs
          ref={tabsRef}
          current={displayedProduct}
          group={group}
          className="mt-5"
        />
      </div>
    </div>
  );
};