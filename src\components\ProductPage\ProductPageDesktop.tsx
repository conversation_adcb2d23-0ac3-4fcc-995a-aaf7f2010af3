import type React from 'react';
import { useEffect, useState, useRef } from 'react';

import { Breadcrumbs } from '@/widgets/breadcrumbs';
import { scrollToRef } from '@/shared/lib/scrollToTop';

import ProductAttributesList from './Attributes/ProductAttributesList';
import CharacteristicsList from './CharacteristicsList';
import ProductGallery from './ProductGallery';
import CharactersticsTab from './ProductTabs/CharactersticsTab';
import FilesTab from './ProductTabs/FilesTab';
import ReviewsTab from './ProductTabs/ReviewsTab';
import InfoTab from './ProductTabs/InfoTab';
import RightBar from './RightBar';
import TopControls from './TopControls';

import type { Product } from '@/entities/product/Product';
import type { ProductGroup } from '@/entities/product/ProductGroup/ProductGroup';
import type { AttributesValuesList } from '@hooks/useModificationAttributesManager';

type ProductPageDesktopProps = {
  group: ProductGroup;
  currentProduct: Product | null;
  attributesList: AttributesValuesList;
  handleChangeAttribute: (event: React.MouseEvent<HTMLDivElement>) => void;
};
export const ProductPageDesktop = ({
  group,
  currentProduct,
  attributesList,
  handleChangeAttribute,
}: ProductPageDesktopProps) => {
  const [tabIndex, setTabIndex] = useState<number>(0);
  const [displayedProduct, setDisplayedProduct] = useState<Product | null>(
    currentProduct
  );
  const tabsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (currentProduct) {
      setDisplayedProduct(currentProduct);
    }
  }, [currentProduct]);

  // Function to switch to a specific tab and scroll to it
  const switchToTab = (index: number) => {
    setTabIndex(index);
    // Scroll to tabs section with a small offset for better UX
    setTimeout(() => {
      scrollToRef(tabsRef, 100);
    }, 50);
  };

  return (
    <div className="content lining-nums proportional-nums">
      <div className="flex justify-between items-center">
        <Breadcrumbs breadcrumbs={group?.category_chain} />
        <TopControls
          product={displayedProduct}
          reviews={group?.reviews}
          onReviewsClick={() => switchToTab(2)}
        />
      </div>
     
      <div className="flex flex-wrap pb-5 min-h-[420px] gap-5">
        <div className="lg:basis-[calc(42%-40px/3)] basis-full max-w-[100vw] lg:max-w-[calc(42%-40px/3)]">
          <ProductGallery
            files={displayedProduct?.files}
            product={displayedProduct}
          />
        </div>
        <div className="lg:basis-[calc(33%-40px/3)] flex flex-col gap-3 basis-full">
          {currentProduct?.brand ? (
            <img
              src={currentProduct.brand.files[0]?.small}
              className="w-20"
              alt=""
            />
          ) : null}
        <div className=" text-xl font-semibold">
          {displayedProduct?.fullName}
        </div>
          <ProductAttributesList
            current={currentProduct}
            attributesList={attributesList}
            handleChangeAttribute={handleChangeAttribute}
            group={group}
          />
            <CharacteristicsList
              current={currentProduct}
              product={group}
              setTabIndex={switchToTab}
            />
        </div>
        <div className="lg:basis-[calc(25%-40px/3)] basis-full">
          <RightBar product={displayedProduct} />
        </div>
      </div>
      <div className="lg:block hidden pb-5 min-h-[420px] gap-5" ref={tabsRef}>
        {/* Custom Tabs Implementation */}
        <div className="w-full mt-5">
          {/* Tab List */}
          <div className="w-full flex flex-wrap gap-[10px]">
            <button
              onClick={() => setTabIndex(0)}
              className={`cursor-pointer text-lg border-2 rounded-lg flex justify-center items-center p-3 basis-[calc(50%-5px)] sm:basis-[calc(25%-7.5px)] hover:bg-colLightGray focus:outline-none focus:ring-2 focus:ring-colGreen/50 ${
                tabIndex === 0
                  ? 'bg-colLightGray border-colGray'
                  : 'border-colLightGray'
              }`}
            >
              Характеристика и описание
            </button>
            <button
              onClick={() => setTabIndex(1)}
              className={`cursor-pointer text-lg border-2 rounded-lg flex justify-center items-center p-3 basis-[calc(50%-5px)] sm:basis-[calc(25%-7.5px)] hover:bg-colLightGray focus:outline-none focus:ring-2 focus:ring-colGreen/50 ${
                tabIndex === 1
                  ? 'bg-colLightGray border-colGray'
                  : 'border-colLightGray'
              }`}
            >
              Документы и сертификаты
            </button>
            <button
              onClick={() => setTabIndex(2)}
              className={`cursor-pointer text-lg border-2 rounded-lg flex justify-center items-center p-3 basis-[calc(50%-5px)] sm:basis-[calc(25%-7.5px)] hover:bg-colLightGray focus:outline-none focus:ring-2 focus:ring-colGreen/50 ${
                tabIndex === 2
                  ? 'bg-colLightGray border-colGray'
                  : 'border-colLightGray'
              }`}
            >
              Отзывы
            </button>
            <button
              onClick={() => setTabIndex(3)}
              className={`cursor-pointer text-lg border-2 rounded-lg flex justify-center items-center p-3 basis-[calc(50%-5px)] sm:basis-[calc(25%-7.5px)] hover:bg-colLightGray focus:outline-none focus:ring-2 focus:ring-colGreen/50 ${
                tabIndex === 3
                  ? 'bg-colLightGray border-colGray'
                  : 'border-colLightGray'
              }`}
            >
              Доставка и оплата
            </button>
          </div>

          {/* Tab Panels */}
          <div className="mt-5">
            {tabIndex === 0 && (
              <div>
                <CharactersticsTab current={displayedProduct} group={group} />
              </div>
            )}
            {tabIndex === 1 && (
              <div>
                <FilesTab product={group} />
              </div>
            )}
            {tabIndex === 2 && (
              <div>
                <ReviewsTab current={displayedProduct} />
              </div>
            )}
            {tabIndex === 3 && (
              <div>
                <InfoTab />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};